package nl.dpes.job.poster.api.database

import cats.effect.{IO, Resource}
import doobie.hikari.HikariTransactor
import doobie.{ExecutionContexts, Transactor}
import org.testcontainers.containers.MySQLContainer

trait DatabaseGenerator {

  def database: Resource[IO, MySQLContainer[Nothing]] = {
    val acquire = IO {
      val container: MySQLContainer[Nothing] = new MySQLContainer("mysql:8.0.35-debian")
      container.start()
      container
    }
    val release = (container: MySQLContainer[Nothing]) => IO(container.stop())
    Resource.make(acquire)(release)
  }

  def transactor: Resource[IO, Transactor[IO]] = for {
    db <- database
    ec <- ExecutionContexts.fixedThreadPool[IO](16)
    xa <- HikariTransactor.newHikariTransactor[IO](
      "org.postgresql.Driver",
      db.getJdbcUrl,
      db.getUsername,
      db.getPassword,
      ec
    )
  } yield xa
}
